import 'dart:io';
import 'package:path_provider/path_provider.dart';

class DataCopyHelper {
  /// Copy files từ thư mục data trong project đến thư mục gpdata trong Documents
  /// Nếu file đã tồn tại thì sẽ bỏ qua
  static Future<void> copyDataToDocuments() async {
    try {
      // Lấy thư mục Documents của máy tính
      final documentsDirectory = await getApplicationDocumentsDirectory();
      
      // Tạo thư mục gpdata trong Documents
      final gpDataDirectory = Directory('${documentsDirectory.path}/gpdata');
      if (!await gpDataDirectory.exists()) {
        await gpDataDirectory.create(recursive: true);
        print('Đã tạo thư mục gpdata: ${gpDataDirectory.path}');
      }
      
      // Thư mục data trong project
      final projectDataDirectory = Directory('data');
      
      if (!await projectDataDirectory.exists()) {
        print('Thư mục data không tồn tại trong project');
        return;
      }
      
      // Copy tất cả files từ thư mục data
      await _copyDirectoryRecursively(projectDataDirectory, gpDataDirectory);
      
      print('Đã copy thành công tất cả files từ data đến ${gpDataDirectory.path}');
    } catch (e) {
      print('Lỗi khi copy database: $e');
    }
  }

  /// Copy một thư mục và tất cả nội dung bên trong một cách đệ quy
  static Future<void> _copyDirectoryRecursively(Directory source, Directory destination) async {
    // Tạo thư mục đích nếu chưa tồn tại
    if (!await destination.exists()) {
      await destination.create(recursive: true);
    }
    
    // Lấy danh sách tất cả files và folders trong thư mục nguồn
    await for (final entity in source.list(recursive: false)) {
      if (entity is File) {
        // Copy file
        final fileName = entity.path.split('/').last;
        final destinationFile = File('${destination.path}/$fileName');
        
        // Kiểm tra nếu file đã tồn tại thì bỏ qua
        if (await destinationFile.exists()) {
          print('File $fileName đã tồn tại, bỏ qua');
          continue;
        }
        
        await entity.copy(destinationFile.path);
        print('Đã copy file: $fileName');
        
      } else if (entity is Directory) {
        // Copy thư mục con
        final dirName = entity.path.split('/').last;
        final destinationDir = Directory('${destination.path}/$dirName');
        
        await _copyDirectoryRecursively(entity, destinationDir);
      }
    }
  }

  /// Lấy đường dẫn đến thư mục gpdata trong Documents
  static Future<String> getGpDataPath() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    return '${documentsDirectory.path}/gpdata';
  }

  /// Kiểm tra xem thư mục gpdata có tồn tại không
  static Future<bool> isGpDataExists() async {
    final gpDataPath = await getGpDataPath();
    return await Directory(gpDataPath).exists();
  }

  /// Xóa thư mục gpdata và tất cả nội dung bên trong
  static Future<void> deleteGpData() async {
    try {
      final gpDataPath = await getGpDataPath();
      final gpDataDirectory = Directory(gpDataPath);
      
      if (await gpDataDirectory.exists()) {
        await gpDataDirectory.delete(recursive: true);
        print('Đã xóa thư mục gpdata: $gpDataPath');
      } else {
        print('Thư mục gpdata không tồn tại');
      }
    } catch (e) {
      print('Lỗi khi xóa thư mục gpdata: $e');
    }
  }

  /// Copy một file cụ thể từ thư mục data đến gpdata
  static Future<void> copySpecificFile(String fileName) async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final gpDataDirectory = Directory('${documentsDirectory.path}/gpdata');
      
      // Tạo thư mục gpdata nếu chưa tồn tại
      if (!await gpDataDirectory.exists()) {
        await gpDataDirectory.create(recursive: true);
      }
      
      // File nguồn trong thư mục data
      final sourceFile = File('data/$fileName');
      if (!await sourceFile.exists()) {
        print('File $fileName không tồn tại trong thư mục data');
        return;
      }
      
      // File đích trong thư mục gpdata
      final destinationFile = File('${gpDataDirectory.path}/$fileName');
      
      // Kiểm tra nếu file đã tồn tại thì bỏ qua
      if (await destinationFile.exists()) {
        print('File $fileName đã tồn tại trong gpdata, bỏ qua');
        return;
      }
      
      await sourceFile.copy(destinationFile.path);
      print('Đã copy file $fileName đến gpdata');
      
    } catch (e) {
      print('Lỗi khi copy file $fileName: $e');
    }
  }
}
