import 'package:async_task/async_task_extension.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/main.app.dart';
import 'package:gp_fbwp_crawler/config/app_configs.dart';
import 'package:gp_fbwp_crawler/config/bootstrap.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:isar/isar.dart';
import 'package:talker_flutter/talker_flutter.dart';

import 'app/features/crawler/mixin/upload_mixin.dart';
import 'data/data_source/local/workplace_local.service.dart';
import 'data/model/workplace/enums/workplace_enums.dart';
import 'helpers/file_helper.dart';

void main() async {
  await initApp(AppConfigProd());

  runApp(const GPCrawlerApp());

  _logIndex();

  _updateTotalDownloadSize().then((value) => _updateTotalUploadSize());
}

Future _copyDatabase() async {
  
}

Future _logIndex() async {
  late final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');
  final checkPoints = await isar.crawlCheckpoints.where().findAll();

  final cCheckPoint = checkPoints
      .firstWhereOrNull((p0) => p0.crawlType == GPBaseCrawlType.thread);

  if (cCheckPoint == null) return;

  final users = await isar.workPlaceCommunityMemberEntitys.where().findAll();

  final entity = users
      .firstWhereOrNull((element) => element.id == cCheckPoint.checkpointId);

  if (entity != null) {
    final index = users.indexOf(entity);
    print('thread index -> $index / ${users.length}');
  }

  //

  final conversations =
      await isar.workPlaceConversationEntitys.where().findAll();
  final mCheckPoint = checkPoints
      .firstWhereOrNull((p0) => p0.crawlType == GPBaseCrawlType.message);

  if (mCheckPoint == null) return;

  final mEntity = conversations
      .firstWhereOrNull((element) => element.id == mCheckPoint.checkpointId);

  if (mEntity != null) {
    final index = conversations.indexOf(mEntity);
    print('message index -> $index / ${conversations.length}');
  }
}

Future _updateTotalDownloadSize() async {
  final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');

  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  final directory = await FileHelper.getDownloadDirectory();

  final files = directory.listSync(recursive: true);

  final length = await files.lengthAsync;

  var totalSize = directory
      .listSync(recursive: true)
      .fold(0, (int sum, file) => sum + file.statSync().size);

  final dashboardEntity = await localService.getGPDashBoardEntity();

  if (dashboardEntity != null) {
    dashboardEntity.totalDownloadFile = length;
    dashboardEntity.totalDownloadSize = totalSize;
    await isar.writeTxn(
      () => isar.gPDashboardEntitys.put(dashboardEntity),
    );
  }
}

Future _updateTotalUploadSize() async {
  final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');

  final attachments = await isar.workPlaceAttachmentEntitys
      .filter()
      .gpLinkIsNotEmpty()
      .findAll();
  int totalSize = 0;

  int totalFile = attachments.length;

  for (var element in attachments) {
    totalSize += element.uploadResponse?.size ?? 0;
  }

  final cAttachments = await isar.workPlaceConversationAttachmentsEntitys
      .filter()
      .gpLinkIsNotEmpty()
      .findAll();
  totalFile += cAttachments.length;

  for (var element in cAttachments) {
    totalSize += element.uploadResponse?.size ?? 0;
  }

  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  final dashboardEntity = await localService.getGPDashBoardEntity();

  if (dashboardEntity != null) {
    dashboardEntity.totalUploadSize = totalSize;
    dashboardEntity.totalUploadFile = totalFile;
    await isar.writeTxn(
      () => isar.gPDashboardEntitys.put(dashboardEntity),
    );
  }

  await localService.updateToRelativeFilePath();

  // await _AttachmentHandler().uploadMissingAttachments();
  // await _AttachmentHandler().uploadMissingMessageAttachments();
  print('_AttachmentHandler done');
}

final class _AttachmentHandler with AttachmentHandlerMixin {
  final uploadResponseHandler = UploadResponseHandler();
  final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');

  Future uploadMissingAttachments() async {
    final attachments = await isar.workPlaceAttachmentEntitys
        .filter()
        .gpLinkIsEmpty()
        .and()
        .localFilePathIsNotEmpty()
        .findAll();

    attachments.removeWhere((e) => e.type == AttachmentType.question);

    print('preparing upload -> ${attachments.length}');

    for (var attachmentEntity in attachments) {
      final wpUrl = attachmentEntity.urlDownload();

      final response = await uploadAttachment(
        wpUrl: wpUrl,
        entity: attachmentEntity,
        downloadOutput: DownloadFileOutput(
          isFileDownloaded: true,
          localFilePath: attachmentEntity.localFilePath ?? '',
        ),
        onUploaded: (uploadUrl, uploadId) {
          attachmentEntity.gpLink = uploadUrl;
          attachmentEntity.gpId = uploadId;
        },
        onDownloadFileSuccess: (path) {
          attachmentEntity.localFilePath = path;
        },
      );

      if (response != null) {
        uploadResponseHandler.updateUploadResponse(attachmentEntity, response);
      }
    }

    await localService.saveAttachments(attachments);
    print('attachmentss -> $attachments');
  }

  Future uploadMissingMessageAttachments() async {
    final cAttachmentss = await isar.workPlaceConversationAttachmentsEntitys
        .filter()
        .gpLinkIsEmpty()
        .and()
        .localFilePathIsNotEmpty()
        .findAll();

    print('cAttachmentss -> $cAttachmentss');
  }
}
